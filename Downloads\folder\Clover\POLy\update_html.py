import pandas as pd
import json
import re

def update_html_with_excel_data():
    """Update the HTML file with complete data from the Excel file"""

    # Read the Excel file
    excel_file = "Poly excel.xlsx"
    df = pd.read_excel(excel_file, sheet_name='Sheet1')

    print("Processing Excel data...")

    # Clean and organize the data
    clinic_data = {}

    # Process bank account data (first 25 rows approximately)
    bank_data_rows = df.iloc[:26].copy()  # First section with bank accounts
    bank_data_rows = bank_data_rows[bank_data_rows['Clinic Name'].notna() &
                                   (bank_data_rows['Clinic Name'] != 'TryCare')]

    for _, row in bank_data_rows.iterrows():
        clinic_name = str(row['Clinic Name']).strip()
        if clinic_name and clinic_name != 'nan' and 'POLYCLINIC' in clinic_name.upper() or 'CENTER' in clinic_name.upper():
            if clinic_name not in clinic_data:
                clinic_data[clinic_name] = {
                    'name': clinic_name,
                    'activity': int(row['Activities']) if pd.notna(row['Activities']) else None,
                    'bankAccounts': [],
                    'floors': []
                }

            # Add bank account info
            bank_account = {
                'bankAccount': str(row['Bank account']) if pd.notna(row['Bank account']) else '',
                'accountNumber': str(row['Acount Number']) if pd.notna(row['Acount Number']) else '',
                'iban': str(row['IBAN']) if pd.notna(row['IBAN']) else '',
                'name': str(row['Name']) if pd.notna(row['Name']) else '',
                'bankAccountNumber': str(row['Bank account number']) if pd.notna(row['Bank account number']) else '',
                'mainAccount': str(row['Main account']) if pd.notna(row['Main account']) else '',
                'clovers': str(row['clovers']) if pd.notna(row['clovers']) else '',
                'pos': str(row['POS']) if pd.notna(row['POS']) and str(row['POS']) != 'nan' else ''
            }
            clinic_data[clinic_name]['bankAccounts'].append(bank_account)

    # Add TryCare and Medwell manually
    if 'TryCare' not in clinic_data:
        clinic_data['TryCare'] = {
            'name': 'TryCare',
            'activity': 1211,
            'bankAccounts': [],
            'floors': []
        }

    # Add Medwell polyclinic bank accounts
    medwell_accounts = df[df['Clinic Name'] == 'Medwell polyclinic']
    if not medwell_accounts.empty and 'Medwell polyclinic' not in clinic_data:
        clinic_data['Medwell polyclinic'] = {
            'name': 'Medwell polyclinic',
            'activity': 1205,
            'bankAccounts': [],
            'floors': []
        }
        for _, row in medwell_accounts.iterrows():
            bank_account = {
                'bankAccount': str(row['Bank account']) if pd.notna(row['Bank account']) else '',
                'accountNumber': str(row['Acount Number']) if pd.notna(row['Acount Number']) else '',
                'iban': str(row['IBAN']) if pd.notna(row['IBAN']) else '',
                'name': str(row['Name']) if pd.notna(row['Name']) else '',
                'bankAccountNumber': str(row['Bank account number']) if pd.notna(row['Bank account number']) else '',
                'mainAccount': str(row['Main account']) if pd.notna(row['Main account']) else '',
                'clovers': str(row['clovers']) if pd.notna(row['clovers']) else '',
                'pos': str(row['POS']) if pd.notna(row['POS']) and str(row['POS']) != 'nan' else ''
            }
            clinic_data['Medwell polyclinic']['bankAccounts'].append(bank_account)

    # Process clinic floor data (starting from row with "Clinic Name" header)
    clinic_floor_start = None
    for idx, row in df.iterrows():
        if pd.notna(row['Bank account']) and str(row['Bank account']).strip() == 'Clinic Name':
            clinic_floor_start = idx
            break

    if clinic_floor_start is not None:
        floor_data_rows = df.iloc[clinic_floor_start+1:].copy()
        floor_data_rows = floor_data_rows[floor_data_rows['Bank account'].notna()]

        for _, row in floor_data_rows.iterrows():
            clinic_name = str(row['Bank account']).strip()
            if clinic_name and clinic_name != 'nan' and clinic_name in clinic_data:
                # Add floor and doctor information
                floor_num = str(row['Acount Number']).strip() if pd.notna(row['Acount Number']) else 'N/A'
                clinic_code = str(row['IBAN']).strip() if pd.notna(row['IBAN']) else 'N/A'
                doctor_name = str(row['Name']).strip() if pd.notna(row['Name']) else 'N/A'
                doctor_type = str(row['Clinic Name']).strip() if pd.notna(row['Clinic Name']) else 'Doctor'

                if floor_num != 'N/A' and clinic_code != 'N/A' and doctor_name != 'N/A':
                    # Find or create floor
                    floor_obj = None
                    for floor in clinic_data[clinic_name]['floors']:
                        if str(floor['floor']) == floor_num:
                            floor_obj = floor
                            break

                    if floor_obj is None:
                        # Try to convert floor to int if possible
                        try:
                            floor_val = int(floor_num) if floor_num.isdigit() else floor_num
                        except:
                            floor_val = floor_num
                        floor_obj = {'floor': floor_val, 'codes': []}
                        clinic_data[clinic_name]['floors'].append(floor_obj)

                    # Add code entry
                    floor_obj['codes'].append({
                        'code': clinic_code,
                        'doctor': doctor_name
                    })

    # Convert to list format for JavaScript and filter out non-clinic entries
    clinic_list = [clinic for clinic in clinic_data.values()
                  if 'POLYCLINIC' in clinic['name'].upper() or
                     'CENTER' in clinic['name'].upper() or
                     clinic['name'] in ['TryCare', 'Medwell polyclinic']]

    # Sort clinics by name
    clinic_list.sort(key=lambda x: x['name'])
    
    print(f"Processed {len(clinic_list)} clinics")
    for clinic in clinic_list:
        print(f"  - {clinic['name']}: {len(clinic['bankAccounts'])} bank accounts, {len(clinic['floors'])} floors")
    
    # Read the current HTML file
    with open('Polyclinic_Organization_Chart.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    # Find and replace the clinicData array in the JavaScript
    clinic_data_json = json.dumps(clinic_list, indent=12, ensure_ascii=False)
    
    # Pattern to match the clinicData array
    pattern = r'let clinicData = \[[\s\S]*?\];'
    replacement = f'let clinicData = {clinic_data_json};'
    
    updated_html = re.sub(pattern, replacement, html_content)
    
    # Save the updated HTML file
    output_file = 'Polyclinic_Organization_Chart_Updated.html'
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(updated_html)
    
    print(f"\n✅ HTML file updated successfully!")
    print(f"📄 New file saved as: {output_file}")
    print(f"🏥 Total clinics: {len(clinic_list)}")
    print(f"🏦 Total bank accounts: {sum(len(c['bankAccounts']) for c in clinic_list)}")
    print(f"🏢 Total floor entries: {sum(len(c['floors']) for c in clinic_list)}")
    
    return clinic_list

if __name__ == "__main__":
    update_html_with_excel_data()
