import pandas as pd
import os

def merge_excel_sheets():
    """Read Excel file and merge all sheets into one consolidated table"""

    # File path
    excel_file = "Poly excel.xlsx"

    # Check if file exists
    if not os.path.exists(excel_file):
        print(f"Error: File '{excel_file}' not found in current directory")
        return

    try:
        # Read the Excel file
        print(f"Reading Excel file: {excel_file}")
        print("=" * 60)

        # Get all sheet names first
        excel_file_obj = pd.ExcelFile(excel_file)
        sheet_names = excel_file_obj.sheet_names
        print(f"Found {len(sheet_names)} sheet(s): {sheet_names}")
        print("=" * 60)

        all_dataframes = []

        # Read each sheet and prepare for merging
        for i, sheet_name in enumerate(sheet_names):
            print(f"\nProcessing Sheet {i+1}: '{sheet_name}'")

            # Read the sheet
            df = pd.read_excel(excel_file, sheet_name=sheet_name)

            # Add a column to identify which sheet the data came from
            df['Source_Sheet'] = sheet_name

            print(f"  - Shape: {df.shape} (rows, columns)")
            print(f"  - Columns: {list(df.columns)}")

            all_dataframes.append(df)

        print("\n" + "=" * 60)
        print("MERGING ALL SHEETS INTO ONE TABLE")
        print("=" * 60)

        # Method 1: Simple concatenation (stacking all sheets vertically)
        try:
            merged_df = pd.concat(all_dataframes, ignore_index=True, sort=False)
            print(f"\nMerged Table Shape: {merged_df.shape} (rows, columns)")
            print(f"Merged Table Columns: {list(merged_df.columns)}")

            print("\n" + "=" * 60)
            print("CONSOLIDATED TABLE - FIRST 50 ROWS")
            print("=" * 60)
            print(merged_df.head(50).to_string(max_cols=15))

            if len(merged_df) > 50:
                print(f"\n... and {len(merged_df) - 50} more rows")

            # Save the merged table to a new Excel file
            output_file = "Merged_Poly_Data.xlsx"
            merged_df.to_excel(output_file, index=False)
            print(f"\n✓ Merged data saved to: {output_file}")

            # Also save as CSV for easier viewing
            csv_file = "Merged_Poly_Data.csv"
            merged_df.to_csv(csv_file, index=False)
            print(f"✓ Merged data also saved as CSV: {csv_file}")

            # Show summary by source sheet
            print("\n" + "=" * 60)
            print("SUMMARY BY SOURCE SHEET")
            print("=" * 60)
            sheet_summary = merged_df['Source_Sheet'].value_counts()
            print(sheet_summary)

            # Show data types
            print("\n" + "=" * 60)
            print("DATA TYPES IN MERGED TABLE")
            print("=" * 60)
            print(merged_df.dtypes)

            return merged_df

        except Exception as merge_error:
            print(f"Error during concatenation: {str(merge_error)}")
            print("\nTrying alternative merge approach...")

            # Alternative approach: merge with common columns only
            if len(all_dataframes) > 1:
                # Find common columns across all sheets
                common_cols = set(all_dataframes[0].columns)
                for df in all_dataframes[1:]:
                    common_cols = common_cols.intersection(set(df.columns))

                print(f"Common columns found: {list(common_cols)}")

                if common_cols:
                    # Keep only common columns and merge
                    filtered_dfs = []
                    for df in all_dataframes:
                        filtered_df = df[list(common_cols)]
                        filtered_dfs.append(filtered_df)

                    merged_df = pd.concat(filtered_dfs, ignore_index=True)
                    print(f"\nMerged Table (common columns only) Shape: {merged_df.shape}")
                    print(merged_df.head(20).to_string())

                    # Save this version too
                    merged_df.to_excel("Merged_Poly_Data_Common_Cols.xlsx", index=False)
                    print("✓ Common columns merged data saved to: Merged_Poly_Data_Common_Cols.xlsx")

                    return merged_df

    except Exception as e:
        print(f"Error reading Excel file: {str(e)}")
        print("Make sure you have the required libraries installed:")
        print("pip install pandas openpyxl")

if __name__ == "__main__":
    merge_excel_sheets()
