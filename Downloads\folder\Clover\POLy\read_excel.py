import pandas as pd
import os

def read_excel_file():
    """Read and display the contents of Poly excel.xlsx"""
    
    # File path
    excel_file = "Poly excel.xlsx"
    
    # Check if file exists
    if not os.path.exists(excel_file):
        print(f"Error: File '{excel_file}' not found in current directory")
        return
    
    try:
        # Read the Excel file
        print(f"Reading Excel file: {excel_file}")
        print("=" * 50)
        
        # Get all sheet names first
        excel_file_obj = pd.ExcelFile(excel_file)
        sheet_names = excel_file_obj.sheet_names
        print(f"Found {len(sheet_names)} sheet(s): {sheet_names}")
        print("=" * 50)
        
        # Read and display each sheet
        for i, sheet_name in enumerate(sheet_names):
            print(f"\nSheet {i+1}: '{sheet_name}'")
            print("-" * 30)
            
            # Read the sheet
            df = pd.read_excel(excel_file, sheet_name=sheet_name)
            
            # Display basic info about the sheet
            print(f"Shape: {df.shape} (rows, columns)")
            print(f"Columns: {list(df.columns)}")
            
            # Display the data
            print("\nData preview:")
            print(df.to_string(max_rows=20, max_cols=10))
            
            # If there are more rows, show summary
            if len(df) > 20:
                print(f"\n... and {len(df) - 20} more rows")
            
            print("\n" + "=" * 50)
        
        # Display summary statistics if numeric data exists
        for sheet_name in sheet_names:
            df = pd.read_excel(excel_file, sheet_name=sheet_name)
            numeric_cols = df.select_dtypes(include=['number']).columns
            
            if len(numeric_cols) > 0:
                print(f"\nNumeric summary for sheet '{sheet_name}':")
                print(df[numeric_cols].describe())
                print("=" * 50)
    
    except Exception as e:
        print(f"Error reading Excel file: {str(e)}")
        print("Make sure you have the required libraries installed:")
        print("pip install pandas openpyxl")

if __name__ == "__main__":
    read_excel_file()
