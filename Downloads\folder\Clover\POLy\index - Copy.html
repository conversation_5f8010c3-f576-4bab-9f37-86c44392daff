<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Polyclinic Directory</title>
    <style>
        /* General Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }

        .main-container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header-actions {
            margin-top: 20px;
        }

        .action-btn {
            background-color: #ffc107;
            color: #212529;
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 0 10px;
        }

        .action-btn:hover {
            background-color: #ffca2c;
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(0,0,0,0.2);
        }
        
        .tab-navigation {
            display: flex;
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }

        .tab-btn {
            padding: 15px 25px;
            cursor: pointer;
            border: none;
            background-color: transparent;
            font-size: 1rem;
            font-weight: 600;
            color: #495057;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }

        .tab-btn.active {
            color: #007bff;
            border-bottom: 3px solid #007bff;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* Card View Specific Styles */
        #cardView {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        #cardView .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        #cardView .controls {
            padding: 20px 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            align-items: center;
        }
        #cardView .search-container { flex: 1; min-width: 300px; position: relative; }
        #cardView #searchInput { width: 100%; padding: 12px 45px 12px 15px; border: 2px solid #dee2e6; border-radius: 25px; font-size: 16px; transition: all 0.3s ease; }
        #cardView #searchInput:focus { outline: none; border-color: #007bff; box-shadow: 0 0 0 3px rgba(0,123,255,0.25); }
        #cardView .search-icon { position: absolute; right: 15px; top: 50%; transform: translateY(-50%); color: #6c757d; }
        #cardView .filter-buttons { display: flex; gap: 10px; flex-wrap: wrap; }
        #cardView .filter-btn { padding: 8px 16px; border: 2px solid #007bff; background: white; color: #007bff; border-radius: 20px; cursor: pointer; transition: all 0.3s ease; font-size: 14px; }
        #cardView .filter-btn:hover, #cardView .filter-btn.active { background: #007bff; color: white; }
        #cardView .stats { display: flex; gap: 20px; margin-left: auto; }
        #cardView .stat-item { text-align: center; padding: 10px; background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); min-width: 80px; }
        #cardView .stat-number { font-size: 1.5rem; font-weight: bold; color: #007bff; }
        #cardView .stat-label { font-size: 0.8rem; color: #6c757d; margin-top: 2px; }
        #cardView .content { padding: 30px; }
        #cardView .clinic-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 25px; margin-top: 20px; }
        #cardView .clinic-card { background: white; border-radius: 12px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); overflow: hidden; transition: all 0.3s ease; border: 1px solid #e9ecef; }
        #cardView .clinic-card:hover { transform: translateY(-5px); box-shadow: 0 8px 25px rgba(0,0,0,0.15); }
        #cardView .clinic-header { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 20px; cursor: pointer; position: relative; }
        #cardView .clinic-header h3 { font-size: 1.3rem; margin-bottom: 5px; }
        #cardView .clinic-meta { font-size: 0.9rem; opacity: 0.9; }
        #cardView .expand-icon { position: absolute; right: 20px; top: 50%; transform: translateY(-50%); font-size: 1.2rem; transition: transform 0.3s ease; }
        #cardView .clinic-card.expanded .expand-icon { transform: translateY(-50%) rotate(180deg); }
        #cardView .clinic-content { max-height: 0; overflow: hidden; transition: max-height 0.3s ease; }
        #cardView .clinic-card.expanded .clinic-content { max-height: 1000px; }
        #cardView .floor-section { padding: 15px 20px; border-bottom: 1px solid #f1f3f4; }
        #cardView .floor-section:last-child { border-bottom: none; }
        #cardView .floor-header { font-weight: bold; color: #495057; margin-bottom: 10px; font-size: 1.1rem; }
        #cardView .codes-container { display: flex; flex-wrap: wrap; gap: 10px; }
        #cardView .code-item { position: relative; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 10px 12px; flex: 1; min-width: 200px; transition: all 0.3s ease; cursor: pointer; }
        #cardView .code-item:hover { background: #e9ecef; transform: scale(1.02); }
        #cardView .code-label { font-weight: bold; color: #007bff; font-size: 0.9rem; }
        #cardView .doctor-name { color: #495057; font-size: 0.85rem; margin-top: 3px; line-height: 1.3; }
        #cardView .highlight { background-color: #fff3cd; padding: 2px 4px; border-radius: 3px; font-weight: bold; }
        #cardView .no-results { text-align: center; padding: 40px; color: #6c757d; font-size: 1.1rem; }
        #cardView .result-count { margin: 20px 0; padding: 10px 15px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; color: #155724; font-weight: bold; }
        #cardView .modal { display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); }
        #cardView .modal-content { background-color: white; margin: 10% auto; padding: 30px; border-radius: 12px; width: 90%; max-width: 500px; box-shadow: 0 10px 30px rgba(0,0,0,0.3); }
        #cardView .close { color: #aaa; float: right; font-size: 28px; font-weight: bold; cursor: pointer; line-height: 1; }
        #cardView .close:hover { color: #000; }

        #cardView .card-actions {
            position: absolute;
            top: 20px;
            right: 50px;
            display: flex;
            gap: 15px;
        }

        #cardView .item-actions {
            position: absolute;
            top: 10px;
            right: 10px;
            display: flex;
            gap: 8px;
        }

        #cardView .edit-icon, #cardView .delete-icon {
            cursor: pointer;
            font-size: 1.1rem;
            transition: transform 0.2s;
        }

        #cardView .edit-icon:hover, #cardView .delete-icon:hover {
            transform: scale(1.2);
        }

        /* Table View Specific Styles */
        #tableView .controls { padding: 20px 30px; background: #f8f9fa; border-bottom: 1px solid #e9ecef; display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; align-items: end; }
        #tableView .control-group { display: flex; flex-direction: column; }
        #tableView .control-group label { margin-bottom: 8px; font-weight: bold; color: #495057; font-size: 0.9rem; }
        #tableView .control-group input, #tableView .control-group select { width: 100%; padding: 12px 15px; border: 2px solid #dee2e6; border-radius: 8px; font-size: 16px; transition: all 0.3s ease; background-color: white; }
        #tableView .control-group input:focus, #tableView .control-group select:focus { outline: none; border-color: #007bff; box-shadow: 0 0 0 3px rgba(0,123,255,0.25); }
        #tableView .stats { padding: 15px 30px; background: #e9ecef; border-bottom: 1px solid #dee2e6; text-align: right; font-weight: bold; color: #155724; }
        #tableView .content { padding: 30px; }
        #tableView .table-container { overflow-x: auto; -webkit-overflow-scrolling: touch; }
        #tableView table { width: 100%; border-collapse: collapse; font-size: 0.95rem; }
        #tableView th, #tableView td { padding: 12px 15px; border: 1px solid #dee2e6; text-align: left; vertical-align: middle; }
        #tableView thead { background-color: #007bff; color: white; }
        #tableView th { font-weight: 600; }
        #tableView tbody tr:nth-child(even) { background-color: #f8f9fa; }
        #tableView tbody tr:hover { background-color: #e9ecef; }
        #tableView .highlight { background-color: #fff3cd; padding: 1px 3px; border-radius: 3px; font-weight: bold; }
        #tableView #noResultsMessage { text-align: center; padding: 40px; color: #6c757d; font-size: 1.1rem; display: none; }

        #tableView .actions-cell {
            text-align: center;
        }

        #tableView .edit-btn, #tableView .delete-btn {
            border: none;
            background: none;
            cursor: pointer;
            font-size: 0.9rem;
            padding: 5px 10px;
            border-radius: 5px;
            transition: background-color 0.2s;
        }

        #tableView .edit-btn {
            color: #007bff;
        }
        #tableView .edit-btn:hover {
            background-color: #e0e0e0;
        }

        #tableView .delete-btn {
            color: #dc3545;
        }
        #tableView .delete-btn:hover {
            background-color: #e0e0e0;
        }

        /* Generic Modal Styles */
        .modal { display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; overflow: auto; background-color: rgba(0,0,0,0.6); }
        .modal-content { background-color: #fefefe; margin: 10% auto; padding: 30px; border-radius: 12px; width: 90%; max-width: 600px; box-shadow: 0 10px 30px rgba(0,0,0,0.3); position: relative; }
        .close { color: #aaa; position: absolute; top: 15px; right: 25px; font-size: 28px; font-weight: bold; cursor: pointer; }
        .close:hover, .close:focus { color: black; text-decoration: none; cursor: pointer; }
        
        #editForm .form-group {
            margin-bottom: 15px;
        }
        #editForm label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        #editForm input, #editForm select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        #editForm button {
            background-color: #28a745;
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
            width: 100%;
        }
        #editForm button:hover {
            background-color: #218838;
        }

    </style>
</head>
<body>
    <div class="main-container">
        <div class="header">
            <h1>🏥 Polyclinic Directory</h1>
            <div class="header-actions">
                <button id="addClinicBtn" class="action-btn">➕ Add New Entry</button>
                <button id="exportHtmlBtn" class="action-btn" style="display: none;">💾 Export Updated HTML</button>
            </div>
        </div>

        <div class="tab-navigation">
            <button class="tab-btn active" data-tab="cardView">Card View</button>
            <button class="tab-btn" data-tab="tableView">Table View</button>
        </div>

        <div id="cardView" class="tab-content active">
            <div class="controls">
                <div class="search-container">
                    <input type="text" id="cardSearchInput" placeholder="Search clinics, doctors, floors...">
                    <span class="search-icon">🔍</span>
                </div>
                <div class="filter-buttons">
                    <button class="filter-btn active" data-filter="all">All</button>
                    <button class="filter-btn" data-filter="doctors">Doctors</button>
                    <button class="filter-btn" data-filter="companies">Companies</button>
                </div>
                <div class="stats">
                    <div class="stat-item"><div class="stat-number" id="totalClinics">0</div><div class="stat-label">Clinics</div></div>
                    <div class="stat-item"><div class="stat-number" id="totalFloors">0</div><div class="stat-label">Floors</div></div>
                    <div class="stat-item"><div class="stat-number" id="totalEntries">0</div><div class="stat-label">Entries</div></div>
                </div>
            </div>
            <div class="content">
                <div id="resultCount" class="result-count" style="display: none;"></div>
                <div id="clinicGrid" class="clinic-grid"></div>
                <div id="noResults" class="no-results" style="display: none;">
                    <h3>No results found</h3>
                    <p>Try adjusting your search terms or filters</p>
                </div>
            </div>
            <div id="detailModal" class="modal">
                <div class="modal-content">
                    <span class="close">&times;</span>
                    <div id="modalContent"></div>
                </div>
            </div>
        </div>

        <div id="tableView" class="tab-content">
            <div class="controls">
                <div class="control-group">
                    <label for="tableSearchInput">Search All Fields</label>
                    <input type="text" id="tableSearchInput" placeholder="e.g., Al Aseel, Dr. Ali, Floor 10...">
                </div>
                <div class="control-group">
                    <label for="clinicFilter">Filter by Clinic Name</label>
                    <select id="clinicFilter"></select>
                </div>
                <div class="control-group">
                    <label for="floorFilter">Filter by Floor</label>
                    <select id="floorFilter"></select>
                </div>
                <div class="control-group">
                    <label for="typeFilter">Filter by Type</label>
                    <select id="typeFilter"></select>
                </div>
            </div>
            <div class="stats">
                <span id="tableResultCount"></span>
            </div>
            <div class="content">
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Clinic Name</th>
                                <th>Floor</th>
                                <th>Clinic Code</th>
                                <th>Doctor/Company</th>
                                <th>Type</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="clinicTableBody"></tbody>
                    </table>
                </div>
                <div id="noResultsMessage">
                    <h3>No results found</h3>
                    <p>Try adjusting your search or filter criteria.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Add/Edit Modal -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <span class="close" id="editModalClose">&times;</span>
            <h2 id="editModalTitle">Add/Edit</h2>
            <form id="editForm">
                <input type="hidden" id="edit-type">
                <input type="hidden" id="original-clinic-name">
                <input type="hidden" id="original-floor">
                <input type="hidden" id="original-code">

                <div class="form-group">
                    <label for="clinicNameInput">Clinic Name</label>
                    <input type="text" id="clinicNameInput" required>
                    <select id="clinicNameSelect"></select>
                </div>
                <div class="form-group">
                    <label for="floorInput">Floor</label>
                    <input type="text" id="floorInput" required>
                </div>
                <div class="form-group">
                    <label for="codeInput">Clinic Code</label>
                    <input type="text" id="codeInput" required>
                </div>
                <div class="form-group">
                    <label for="doctorInput">Doctor/Company</label>
                    <input type="text" id="doctorInput" required>
                </div>
                <button type="submit" id="saveChangesBtn">Save Changes</button>
            </form>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const clinicData = [
                { "name": "Iris - Polyclinic Mazaya 3", "floors": [ { "floor": 2, "codes": [{"code": "A", "doctor": "Abdullah Abdul Aziz Al Hajri"}, {"code": "B", "doctor": "Smart Health Co."}, {"code": "C", "doctor": "Walid Hamad Ashwi Raheel"}] }, { "floor": 3, "codes": [{"code": "A+B", "doctor": "Amr Nabil Qutb"}, {"code": "C", "doctor": "Oxcana Bogdanovic"}] }, { "floor": 4, "codes": [{"code": "A", "doctor": "Hassaan A Jaber & Obaid Metni"}, {"code": "B", "doctor": "Mohamed Youssef Al Eissa"}, {"code": "C", "doctor": "Dr. Mariam Abed Ali Al-Turki"}] } ] },
                { "name": "Al Aseel International", "floors": [ { "floor": 7, "codes": [{"code": "C", "doctor": "Daniel Alain"}] }, { "floor": 8, "codes": [{"code": "A", "doctor": "Abdullah Abdul Rahman Al Hassan"}, {"code": "B", "doctor": "Hossam Mohamed El Badri"}, {"code": "C", "doctor": "Mustafa Samy Al Kaddousy"}] }, { "floor": 9, "codes": [{"code": "A", "doctor": "Nasser Faisal Al Mutairy"}, {"code": "B", "doctor": "Andro George Mikha'eel"}, {"code": "C", "doctor": "Dr. Noor Aladdin Alomar & Dr. Aya Samara"}] }, { "floor": 10, "codes": [{"code": "A+B", "doctor": "Dr. Ali Al-Mukaimi & Nisreen Baeij"}, {"code": "C", "doctor": "Dr. Ali Al-Mukaimi"}] } ] },
                { "name": "Yarow - Polyclinic", "floors": [ { "floor": 11, "codes": [{"code": "A", "doctor": "Dr. Ahmed Abdulsamad Yehya Jassem"}, {"code": "B", "doctor": "Dr. Osamah J M Albaker"}, {"code": "C", "doctor": "Hossam Mohamed El Badri"}] }, { "floor": 12, "codes": [{"code": "A", "doctor": "Ahmed Mohamed Ahmed Ibrahim"}, {"code": "B", "doctor": "Sale Abdul Ghaffar Ma'arafie"}, {"code": "C", "doctor": "Adnan Ibrahim Ibrahim"}] } ] },
                { "name": "Fourth Medical Center", "floors": [ {"floor": 1, "codes": [{"code": "A", "doctor": "Salam Attar"}]}, {"floor": "2-3", "codes": [{"code": "A", "doctor": "One Day to Manage Projects Co."}]}, {"floor": "4-5", "codes": [{"code": "A", "doctor": "Athba Co."}]}, {"floor": 6, "codes": [{"code": "A", "doctor": "Health Care Co."}]}, {"floor": 7, "codes": [{"code": "A", "doctor": "Abdul Aziz Fahad Al Mezeiny"}]}, {"floor": 13, "codes": [{"code": "A", "doctor": "Revolution Medical Co."}]}, {"floor": 14, "codes": [{"code": "A", "doctor": "Dr. Farouk Alzoubani"}]}, {"floor": 15, "codes": [{"code": "A", "doctor": "Assem Drwesh Mostafa Abdulnabi"}]}, {"floor": 16, "codes": [{"code": "A", "doctor": "One Day to Manage Projects Co."}]}, {"floor": 17, "codes": [{"code": "A", "doctor": "Dr. Abdullah Sadad Sabri Al-Ozairi"}]}, {"floor": "18-19", "codes": [{"code": "A", "doctor": "Gulf Care Co."}]} ] },
                { "name": "Medical Harbour", "floors": [ {"floor": 1, "codes": [{"code": "C", "doctor": "Moaeyed Zaid Al Saq'abi"}]}, {"floor": 2, "codes": [{"code": "C", "doctor": "Mohamed Abdul Majid Hassan"}]}, {"floor": 3, "codes": [{"code": "C", "doctor": "Salah El Din Mohamed El Sherbini"}]}, {"floor": 4, "codes": [{"code": "C", "doctor": "Youssef Al Khleify/Rawan Al Khatib"}]}, {"floor": 8, "codes": [{"code": "C", "doctor": "Amir Eissa Attia Killa"}]}, {"floor": 9, "codes": [{"code": "C", "doctor": "Dr. Hesham Mohamed Yassin Ibrahim"}]}, {"floor": 10, "codes": [{"code": "C", "doctor": "Med Vision Medical Services"}]}, {"floor": 11, "codes": [{"code": "C", "doctor": "Fatmah Mohamed Badawy"}]}, {"floor": 12, "codes": [{"code": "C", "doctor": "Othman Youssef Al Mas'oud"}]}, {"floor": 13, "codes": [{"code": "C", "doctor": "Btissam Ibn Kiran"}]}, {"floor": 14, "codes": [{"code": "C", "doctor": "Misha'al Al Dahsh"}]}, {"floor": 15, "codes": [{"code": "C", "doctor": "Amal Al Shaiji / Faisal Al Terkeet"}]}, {"floor": 16, "codes": [{"code": "C", "doctor": "Signofa Co./Ahmed Eissa"}]}, {"floor": 17, "codes": [{"code": "C", "doctor": "Waleed Hamid Raheel"}]}, {"floor": 18, "codes": [{"code": "C", "doctor": "Eman Ghorab"}]}, {"floor": 19, "codes": [{"code": "C", "doctor": "Emad Morkos/Ahmed Youssef"}]}, {"floor": 20, "codes": [{"code": "C", "doctor": "Mohamed Al Kolk"}]}, {"floor": 21, "codes": [{"code": "C", "doctor": "Youssef Al Khleify"}]} ] },
                { "name": "Med Marine", "floors": [ {"floor": 5, "codes": [{"code": "A", "doctor": "Fatima Ne'ma Al Awadhi"}, {"code": "B", "doctor": "Mohamed As'ad Eid/Wael Bezrah"}]}, {"floor": 6, "codes": [{"code": "A+B", "doctor": "Mohamed Youssef Al Sabty"}]}, {"floor": 7, "codes": [{"code": "A+B", "doctor": "Mostafa Mohamed Tomsu"}]} ] },
                { "name": "JOYA - Polyclinic", "floors": [ {"floor": 8, "codes": [{"code": "A", "doctor": "Ihab Mohamed Younes Omar"}, {"code": "B", "doctor": "Huda Mahmoud Selim"}]}, {"floor": 9, "codes": [{"code": "A+B", "doctor": "Berlin Co./Mohamed Riyadh"}]}, {"floor": 10, "codes": [{"code": "A+B", "doctor": "Shehta Mostafa Ze'reb"}]} ] },
                { "name": "Med Grey", "floors": [ {"floor": 5, "codes": [{"code": "A", "doctor": "Dr. Amr Nabil Qutb"}]}, {"floor": "6-7", "codes": [{"code": "A", "doctor": "Dr. Shehta Mostafa Zurub"}]} ] },
                { "name": "Aram - Polyclinic", "floors": [ {"floor": 2, "codes": [{"code": "A+B", "doctor": "Dalia/Mina/Osama/Mahmoud"}]}, {"floor": 3, "codes": [{"code": "A+B", "doctor": "Ayman/Islam"}]}, {"floor": 4, "codes": [{"code": "A", "doctor": "Mohamed Al Sayyad"}, {"code": "B", "doctor": "Mohamed Al Sayyad"}]}, {"floor": 5, "codes": [{"code": "A", "doctor": "Bishoy/Mina/Zaher"}, {"code": "B", "doctor": "Nasser/Mohamed"}]}, {"floor": 6, "codes": [{"code": "A", "doctor": "Munira/Anjoud"}, {"code": "B", "doctor": "Munira/Anjoud"}]}, {"floor": 7, "codes": [{"code": "A+B", "doctor": "Sondos Ghaneim"}]}, {"floor": 8, "codes": [{"code": "A", "doctor": "Marina/Mary/Mariana"}, {"code": "B", "doctor": "Dr. Mohammed Salem"}]}, {"floor": 9, "codes": [{"code": "A", "doctor": "Rwda Ahmed"}, {"code": "B", "doctor": "Marawan Essam"}]} ] }
            ];

            // Tab switching logic
            const tabs = document.querySelectorAll('.tab-btn');
            const tabContents = document.querySelectorAll('.tab-content');
            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    tabs.forEach(t => t.classList.remove('active'));
                    tab.classList.add('active');
                    tabContents.forEach(c => c.classList.remove('active'));
                    document.getElementById(tab.dataset.tab).classList.add('active');
                });
            });

            // --- Card View Logic ---
            const cardSearchInput = document.getElementById('cardSearchInput');
            const clinicGrid = document.getElementById('clinicGrid');
            const resultCountEl = document.getElementById('resultCount');
            const noResultsEl = document.getElementById('noResults');
            let currentFilter = 'all';
            let currentSearch = '';

            function updateStats() {
                document.getElementById('totalClinics').textContent = clinicData.length;
                document.getElementById('totalFloors').textContent = clinicData.reduce((s, c) => s + c.floors.length, 0);
                document.getElementById('totalEntries').textContent = clinicData.reduce((s, c) => s + c.floors.reduce((fs, f) => fs + f.codes.length, 0), 0);
            }

            function debounce(func, wait) {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => { clearTimeout(timeout); func(...args); };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            }

            function handleCardSearch(e) { currentSearch = e.target.value.toLowerCase().trim(); renderClinics(); }
            function handleFilter(e) { document.querySelectorAll('#cardView .filter-btn').forEach(btn => btn.classList.remove('active')); e.target.classList.add('active'); currentFilter = e.target.dataset.filter; renderClinics(); }
            function matchesFilter(doctor) {
                if (currentFilter === 'all') return true;
                const isCo = doctor.toLowerCase().includes('co.') || doctor.toLowerCase().includes('company') || doctor.toLowerCase().includes('services');
                if (currentFilter === 'companies') return isCo;
                if (currentFilter === 'doctors') return !isCo;
                return true;
            }
            function matchesSearch(clinic, floor, code) {
                if (!currentSearch) return true;
                return [clinic.name, `floor ${floor.floor}`, code.code, code.doctor].join(' ').toLowerCase().includes(currentSearch);
            }
            function highlightText(text, search) {
                if (!search) return text;
                const regex = new RegExp(`(${search.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&')})`, 'gi');
                return text.replace(regex, '<span class="highlight">$1</span>');
            }

            function renderClinics() {
                let filteredClinics = [];
                let totalMatches = 0;
                clinicData.forEach(clinic => {
                    const filteredFloors = [];
                    clinic.floors.forEach(floor => {
                        const filteredCodes = floor.codes.filter(code => matchesFilter(code.doctor) && matchesSearch(clinic, floor, code));
                        if (filteredCodes.length > 0) {
                            filteredFloors.push({ ...floor, codes: filteredCodes });
                            totalMatches += filteredCodes.length;
                        }
                    });
                    if (filteredFloors.length > 0) filteredClinics.push({ ...clinic, floors: filteredFloors });
                });

                if (currentSearch || currentFilter !== 'all') {
                    resultCountEl.style.display = 'block';
                    resultCountEl.textContent = `Found ${totalMatches} matching ${totalMatches === 1 ? 'entry' : 'entries'} in ${filteredClinics.length} ${filteredClinics.length === 1 ? 'clinic' : 'clinics'}.`;
                } else {
                    resultCountEl.style.display = 'none';
                }

                clinicGrid.style.display = filteredClinics.length > 0 ? 'grid' : 'none';
                noResultsEl.style.display = filteredClinics.length === 0 ? 'block' : 'none';

                clinicGrid.innerHTML = filteredClinics.map(clinic => `
                    <div class="clinic-card" data-clinic="${clinic.name}">
                        <div class="clinic-header">
                            <h3>${highlightText(clinic.name, currentSearch)}</h3>
                            <div class="clinic-meta">${clinic.floors.length} floors • ${clinic.floors.reduce((s, f) => s + f.codes.length, 0)} entries</div>
                            <div class="card-actions">
                                <span class="edit-icon" data-type="clinic" data-clinic-name="${clinic.name}">✏️</span>
                                <span class="delete-icon" data-type="clinic" data-clinic-name="${clinic.name}">🗑️</span>
                            </div>
                            <span class="expand-icon">▼</span>
                        </div>
                        <div class="clinic-content">
                            ${clinic.floors.map(floor => `
                                <div class="floor-section">
                                    <div class="floor-header">Floor ${highlightText(String(floor.floor), currentSearch)}</div>
                                    <div class="codes-container">
                                        ${floor.codes.map(code => `
                                            <div class="code-item" data-clinic="${clinic.name}" data-floor="${floor.floor}" data-code="${code.code}" data-doctor="${code.doctor}">
                                                <div class="code-label">Clinic ${highlightText(code.code, currentSearch)}</div>
                                                <div class="doctor-name">${highlightText(code.doctor, currentSearch)}</div>
                                                <div class="item-actions">
                                                    <span class="edit-icon" data-type="entry" data-clinic-name="${clinic.name}" data-floor="${floor.floor}" data-code="${code.code}">✏️</span>
                                                    <span class="delete-icon" data-type="entry" data-clinic-name="${clinic.name}" data-floor="${floor.floor}" data-code="${code.code}">🗑️</span>
                                                </div>
                                            </div>
                                        `).join('')}
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `).join('');
            }
            
            function showDetails(clinicName, floor, code, doctor) {
                const modal = document.getElementById('detailModal');
                document.getElementById('modalContent').innerHTML = `<h2>📋 Clinic Details</h2><div style="margin: 20px 0; line-height: 1.6;"><p><strong>🏥 Medical Facility:</strong> ${clinicName}</p><p><strong>🏢 Floor:</strong> ${floor}</p><p><strong>🚪 Clinic Code:</strong> ${code}</p><p><strong>👨‍⚕️ Doctor/Company:</strong> ${doctor}</p></div>`;
                modal.style.display = 'block';
            }

            cardSearchInput.addEventListener('input', debounce(handleCardSearch, 300));
            document.querySelectorAll('#cardView .filter-btn').forEach(btn => btn.addEventListener('click', handleFilter));
            const modal = document.getElementById('detailModal');
            document.querySelector('#cardView .close').addEventListener('click', () => modal.style.display = 'none');
            window.addEventListener('click', (e) => { if (e.target === modal) modal.style.display = 'none'; });
            clinicGrid.addEventListener('click', function(e) {
                const header = e.target.closest('.clinic-header');
                if (header) { header.closest('.clinic-card').classList.toggle('expanded'); return; }
                const codeItem = e.target.closest('.code-item');
                if (codeItem) { const { clinic, floor, code, doctor } = codeItem.dataset; showDetails(clinic, floor, code, doctor); return; }
            });

            updateStats();
            renderClinics();

            // --- Table View Logic ---
            const tableSearchInput = document.getElementById('tableSearchInput');
            const clinicFilter = document.getElementById('clinicFilter');
            const floorFilter = document.getElementById('floorFilter');
            const typeFilter = document.getElementById('typeFilter');
            const tableBody = document.getElementById('clinicTableBody');
            const tableResultCount = document.getElementById('tableResultCount');
            const noResultsMessage = document.getElementById('noResultsMessage');

            const flatData = clinicData.flatMap(c => c.floors.flatMap(f => f.codes.map(code => ({ clinicName: c.name, floor: String(f.floor), clinicCode: code.code, doctorCompany: code.doctor, type: (code.doctor.toLowerCase().includes('co.') || code.doctor.toLowerCase().includes('services')) ? 'Company' : 'Doctor' }))));

            function populateFilters() {
                const createOptions = (select, options, defaultLabel) => {
                    select.innerHTML = `<option value="">${defaultLabel}</option>`;
                    [...new Set(options)].sort().forEach(opt => select.innerHTML += `<option value="${opt}">${opt}</option>`);
                };
                createOptions(clinicFilter, flatData.map(i => i.clinicName), 'All Clinics');
                createOptions(floorFilter, flatData.map(i => i.floor), 'All Floors');
                createOptions(typeFilter, flatData.map(i => i.type), 'All Types');
            }

            function renderTable() {
                const searchTerm = tableSearchInput.value.toLowerCase();
                const selectedClinic = clinicFilter.value;
                const selectedFloor = floorFilter.value;
                const selectedType = typeFilter.value;

                const filteredData = flatData.filter(item => 
                    (!selectedClinic || item.clinicName === selectedClinic) &&
                    (!selectedFloor || item.floor === selectedFloor) &&
                    (!selectedType || item.type === selectedType) &&
                    (Object.values(item).join(' ').toLowerCase().includes(searchTerm))
                );

                tableBody.innerHTML = '';
                tableResultCount.textContent = `Showing ${filteredData.length} of ${flatData.length} entries`;
                noResultsMessage.style.display = filteredData.length === 0 ? 'block' : 'none';

                filteredData.forEach(item => {
                    const row = document.createElement('tr');
                    const highlight = (text) => !searchTerm ? text : text.replace(new RegExp(`(${searchTerm})`, 'gi'), `<span class="highlight">$1</span>`);
                    row.innerHTML = `
                        <td>${highlight(item.clinicName)}</td>
                        <td>${highlight(item.floor)}</td>
                        <td>${highlight(item.clinicCode)}</td>
                        <td>${highlight(item.doctorCompany)}</td>
                        <td>${highlight(item.type)}</td>
                        <td class="actions-cell">
                            <button class="edit-btn" data-clinic-name="${item.clinicName}" data-floor="${item.floor}" data-code="${item.clinicCode}">✏️ Edit</button>
                            <button class="delete-btn" data-clinic-name="${item.clinicName}" data-floor="${item.floor}" data-code="${item.clinicCode}">🗑️ Delete</button>
                        </td>
                    `;
                    tableBody.appendChild(row);
                });
            }

            tableSearchInput.addEventListener('input', renderTable);
            clinicFilter.addEventListener('change', renderTable);
            floorFilter.addEventListener('change', renderTable);
            typeFilter.addEventListener('change', renderTable);

            populateFilters();
            renderTable();
        });
    </script>
</body>
</html>
