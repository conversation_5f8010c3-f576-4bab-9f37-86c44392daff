from msal import PublicClientApplication
import requests
import sys

# Your app credentials
CLIENT_ID = "5499a9b8-cd2e-4d67-b48c-8a6561af6a7e"
TENANT_ID = "186cd511-c644-4845-bf7d-484ed2dd59ae"
AUTHORITY = f"https://login.microsoftonline.com/{TENANT_ID}"
SCOPES = ["Mail.Read"]

# Create the MSAL app
app = PublicClientApplication(CLIENT_ID, authority=AUTHORITY)

# Try to get token silently
accounts = app.get_accounts()
result = None
if accounts:
    result = app.acquire_token_silent(SCOPES, account=accounts[0])

# If no token, prompt user to log in
if not result:
    result = app.acquire_token_interactive(SCOPES)

if not result or "access_token" not in result:
    print("Could not acquire token.")
    if result and "error_description" in result:
        print("Error:", result["error_description"])
    sys.exit(1)


access_token = result["access_token"]

# Define the folder name
folder_name = "Bahrain"

# Make the API call to get unread messages
headers = {"Authorization": f"Bearer {access_token}"}
url = f"https://graph.microsoft.com/v1.0/me/mailFolders('{folder_name}')/messages?$filter=isRead eq false"

response = requests.get(url, headers=headers)

if response.status_code == 200:
    emails = response.json()
    # Print the number of unread emails
    print(f"Unread emails in '{folder_name}':", len(emails.get("value", [])))
else:
    print(f"Error fetching emails: {response.status_code}")
    print(response.json())
